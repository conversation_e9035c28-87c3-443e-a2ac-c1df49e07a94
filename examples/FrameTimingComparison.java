package examples;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 音频帧发送时间精度对比示例
 *
 * 展示了 DialogueService#handleInputText 方法中：
 * 1. 原来的 random.nextInt(6) + 55 (55-60ms随机sleep) 方式
 * 2. 新的精确时间调度方式的区别
 */
public class FrameTimingComparison {
    
    private static final long OPUS_FRAME_DURATION_MS = 60; // OPUS帧持续时间
    private static final int FRAME_COUNT = 10; // 测试帧数量
    
    public static void main(String[] args) throws InterruptedException {
        System.out.println("=== 音频帧发送时间精度对比 ===\n");
        
        // 测试原来的随机 sleep 方式
        System.out.println("1. 原来的 random.nextInt(6) + 55 方式:");
        testOldRandomSleepMethod();
        
        Thread.sleep(1000); // 等待一秒
        
        // 测试新的精确时间调度方式
        System.out.println("\n2. 新的精确时间调度方式:");
        testPreciseTimingMethod();
        
        Thread.sleep(2000); // 等待调度完成
        System.exit(0);
    }
    
    /**
     * 测试原来的随机 sleep 方式 (handleInputText 中的实现)
     */
    private static void testOldRandomSleepMethod() {
        long startTime = System.nanoTime();
        long lastFrameTime = startTime;
        
        System.out.println("帧索引 | 实际间隔(ms) | 期望间隔(ms) | 误差(ms)");
        System.out.println("-------|-------------|-------------|----------");
        
        for (int i = 0; i < FRAME_COUNT; i++) {
            long currentTime = System.nanoTime();
            
            if (i > 0) {
                long actualInterval = (currentTime - lastFrameTime) / 1_000_000;
                long error = actualInterval - OPUS_FRAME_DURATION_MS;
                System.out.printf("  %2d   |    %3d      |     %2d      |   %+3d\n", 
                        i, actualInterval, OPUS_FRAME_DURATION_MS, error);
            } else {
                System.out.printf("  %2d   |     -       |     %2d      |    -\n", 
                        i, OPUS_FRAME_DURATION_MS);
            }
            
            lastFrameTime = currentTime;
            
            // 模拟发送帧
            simulateFrameSend(i);
            
            // 原来的随机 sleep 方式 (handleInputText 中的实现)
            try {
                java.util.Random random = new java.util.Random();
                int sleepTime = random.nextInt(6) + 55; // 55-60ms 随机
                TimeUnit.MILLISECONDS.sleep(sleepTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 测试新的精确时间调度方式
     */
    private static void testPreciseTimingMethod() {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2,
                Thread.ofVirtual().name("precise-timing-", 0).factory());
        
        long startTime = System.nanoTime();
        
        System.out.println("帧索引 | 实际间隔(ms) | 期望间隔(ms) | 误差(ms)");
        System.out.println("-------|-------------|-------------|----------");
        
        // 立即发送第一帧
        long[] lastFrameTime = {startTime};
        simulateFrameSend(0);
        System.out.printf("  %2d   |     -       |     %2d      |    -\n", 
                0, OPUS_FRAME_DURATION_MS);
        
        // 调度剩余帧
        for (int i = 1; i < FRAME_COUNT; i++) {
            final int frameIndex = i;
            
            // 计算预期发送时间
            long expectedTime = startTime + frameIndex * OPUS_FRAME_DURATION_MS * 1_000_000;
            long currentTime = System.nanoTime();
            long delayNanos = expectedTime - currentTime;
            
            Runnable frameTask = () -> {
                long actualTime = System.nanoTime();
                long actualInterval = (actualTime - lastFrameTime[0]) / 1_000_000;
                long error = actualInterval - OPUS_FRAME_DURATION_MS;
                
                System.out.printf("  %2d   |    %3d      |     %2d      |   %+3d\n", 
                        frameIndex, actualInterval, OPUS_FRAME_DURATION_MS, error);
                
                simulateFrameSend(frameIndex);
                lastFrameTime[0] = actualTime;
            };
            
            if (delayNanos <= 0) {
                scheduler.schedule(frameTask, 0, TimeUnit.NANOSECONDS);
            } else {
                scheduler.schedule(frameTask, delayNanos, TimeUnit.NANOSECONDS);
            }
        }
        
        scheduler.shutdown();
    }
    
    /**
     * 模拟发送音频帧
     */
    private static void simulateFrameSend(int frameIndex) {
        // 模拟发送操作的耗时（通常很小）
        try {
            Thread.sleep(1); // 模拟1ms的发送耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
