package com.xiaozhi.communication.server.mqtt.udp;

import com.xiaozhi.communication.server.mqtt.MqttConfig;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import java.net.InetSocketAddress;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;

/**
 * UDP 服务器
 * 用于接收客户端发送的加密音频数据
 * 基于 Netty 实现高性能 UDP 服务器
 */
@Slf4j
@Component
public class UdpServer {

    @Autowired
    private MqttConfig mqttConfig;

    private EventLoopGroup group;
    private Channel channel;
    private volatile boolean running = false;

    // UDP数据包处理器回调
    private BiConsumer<byte[], InetSocketAddress> packetHandler;

    @PostConstruct
    public void start() {
        CompletableFuture.runAsync(this::startServer);
    }

    @PreDestroy
    public void stop() {
        stopServer();
    }

    /**
     * 设置UDP数据包处理器
     */
    public void setPacketHandler(BiConsumer<byte[], InetSocketAddress> handler) {
        this.packetHandler = handler;
    }

    /**
     * 启动 UDP 服务器
     */
    private void startServer() {
        try {
            group = new NioEventLoopGroup(mqttConfig.getUdp().getWorkerThreads());

            Bootstrap bootstrap = new Bootstrap();
            bootstrap.group(group)
                    .channel(NioDatagramChannel.class)
                    .option(ChannelOption.SO_BROADCAST, true)
                    .option(ChannelOption.SO_RCVBUF, mqttConfig.getUdp().getReceiveBufferSize())
                    .option(ChannelOption.SO_SNDBUF, mqttConfig.getUdp().getSendBufferSize())
                    .handler(new UdpServerHandler());

            ChannelFuture future = bootstrap.bind(mqttConfig.getUdp().getHost(), mqttConfig.getUdp().getPort()).sync();
            channel = future.channel();
            running = true;

            log.info("UDP 服务器启动成功 - 地址: {}:{}, 工作线程数: {}",
                    mqttConfig.getUdp().getHost(),
                    mqttConfig.getUdp().getPort(),
                    mqttConfig.getUdp().getWorkerThreads());

            // 等待服务器关闭
            channel.closeFuture().sync();
        } catch (Exception e) {
            log.error("UDP 服务器启动失败", e);
        } finally {
            if (group != null) {
                group.shutdownGracefully();
            }
            running = false;
        }
    }

    /**
     * 停止 UDP 服务器
     */
    private void stopServer() {
        try {
            log.info("开始停止UDP服务器...");
            running = false;

            // 清理数据包处理器
            packetHandler = null;

            if (channel != null) {
                log.debug("关闭UDP通道...");
                channel.close().sync();
                channel = null;
            }

            if (group != null) {
                log.debug("关闭事件循环组...");
                group.shutdownGracefully().sync();
                group = null;
            }

            log.info("UDP 服务器已完全停止");
        } catch (Exception e) {
            log.error("停止 UDP 服务器时发生错误", e);
        }
    }


    /**
     * 发送原始 UDP 消息到指定地址（不加密）
     *
     * @param data   要发送的数据
     * @param target 目标地址
     */
    public void sendMessage(byte[] data, InetSocketAddress target) {
        if (!running || channel == null) {
            log.warn("UDP 服务器未运行，无法发送消息");
            return;
        }

        try {
            ByteBuf buffer = channel.alloc().buffer(data.length);
            buffer.writeBytes(data);
            DatagramPacket packet = new DatagramPacket(buffer, target);
            channel.writeAndFlush(packet);
            // log.debug("发送原始 UDP 消息到 {} - 大小: {} 字节", target, data.length);
        } catch (Exception e) {
            log.error("发送 UDP 消息失败 - 目标: {}", target, e);
        }
    }

    /**
     * 检查服务器是否运行中
     */
    public boolean isRunning() {
        return running;
    }

    /**
     * 获取服务器绑定的地址
     */
    public InetSocketAddress getLocalAddress() {
        if (channel != null && channel.localAddress() instanceof InetSocketAddress) {
            return (InetSocketAddress) channel.localAddress();
        }
        return null;
    }

    /**
     * UDP 服务器处理器
     */
    private class UdpServerHandler extends SimpleChannelInboundHandler<DatagramPacket> {

        @Override
        protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket packet) throws Exception {
            try {
                InetSocketAddress sender = packet.sender();
                ByteBuf content = packet.content();

                // 读取数据
                byte[] data = new byte[content.readableBytes()];
                content.readBytes(data);

                // log.debug("收到 UDP 数据包 - 来源: {}, 大小: {} 字节", sender, data.length);

                // 委托给注册的处理器处理
                if (packetHandler != null) {
                    packetHandler.accept(data, sender);
                } else {
                    log.warn("UDP数据包处理器未设置，忽略数据包");
                }

            } catch (Exception e) {
                log.error("处理 UDP 数据包时发生错误", e);
            }
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            log.error("UDP 服务器处理异常", cause);
        }
    }
}
