package com.xiaozhi.communication.common;

import java.util.HashMap;
import java.util.Map;

public class Constant {

    public static final Map<String, String> LevelContent = new HashMap<>(){{
        put("A1", """
                - 所有输出应保持在英语母语5–6岁儿童的表达难度，帮助孩子在轻松的氛围中练习开口。
                - 当用户回答很短时：重复关键词并提出一个非常简单的好奇问题。
                - 当用户回答较长时：只延展话题一步，简单提问或分享小故事。
                - 单句小于8词
                """);
        put("A2", """
                - 所有输出应保持在英语母语7–8岁儿童的表达难度，帮助孩子在轻松的氛围中练习开口。
                - 当用户回答很短时：重复关键词并提出一个简单的好奇问题。
                - 当用户回答较长时：可以延展话题两步，简单比较、引导多说。
                - 单句小于10词
                """);
        put("B1", """
                - 所有输出应保持在英语母语9-10岁儿童的表达难度，帮助孩子在轻松的氛围中练习开口。
                - 当用户回答很短时：重复关键词并提出一个稍有挑战的问题。
                - 当用户回答较长时：可以延展话题多步，引导解释原因或讲小故事。
                - 单句小于15词
                """);
    }};

}
