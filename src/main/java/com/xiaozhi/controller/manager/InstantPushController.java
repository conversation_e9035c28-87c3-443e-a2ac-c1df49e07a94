package com.xiaozhi.controller.manager;

import com.xiaozhi.common.interceptor.Authorized;
import com.xiaozhi.common.interceptor.PassAuth;
import com.xiaozhi.common.web.AuthorizedUser;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.communication.common.SessionManager;
import com.xiaozhi.communication.server.mqtt.MqttServerPublish;
import com.xiaozhi.dialogue.domain.StartParams;
import com.xiaozhi.dialogue.service.DialogueService;
import com.xiaozhi.service.ManagerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/m")
@Tag(name = "", description = "")
public class InstantPushController {

    @Resource
    private MqttServerPublish mqttServerPublish;

    @Resource
    private SessionManager sessionManager;

    @Resource
    private DialogueService dialogueService;

    @Resource
    private ManagerService managerService;

    @PostMapping("/push")
    @Operation(summary = "主动推送场景")
    public Either<BizError, ?> push(@Authorized AuthorizedUser user, @RequestBody PushParams params) throws InterruptedException {
        var session = sessionManager.getSessionByDeviceId(user.getDeviceId());
        if (session == null) {
            var topic = STR."devices/p2p/GID@@@\{user.getDeviceId().replaceAll(":", "_")}";
            mqttServerPublish.wakeup(topic);
            TimeUnit.SECONDS.sleep(2);
        }

        return Option.of(sessionManager.getSessionByDeviceId(user.getDeviceId()))
                .toEither(BizError.of(400, "设备未连接"))
                .filterOrElse(it -> !it.isPlaying(), _ -> BizError.of(400, "设备说话中，请稍后再试"))
                .flatMap(newSession -> managerService.detail(user.getId())
                        .map(manager -> {
                            var startParams = new StartParams()
                                    .setName(manager.getName())
                                    .setCefr(manager.getCefr())
                                    .setScene(params.scene)
                                    .setUserId(manager.getId().toString())
                                    .setConversationId(manager.getStarterConvId());
                            return dialogueService.sendStartSentence(newSession, startParams);
                        })
                )
                .filterOrElse(it -> it, _ -> BizError.of(400, "设备说话中，请稍后再试"));
    }


    @PostMapping("/pa")
    @Operation(summary = "主动推送场景")
    public Either<BizError, ?> pushAudio(@Authorized AuthorizedUser user, @RequestBody PushParams params) throws InterruptedException {
        var session = sessionManager.getSessionByDeviceId(user.getDeviceId());
        if (session == null) {
            var topic = STR."devices/p2p/GID@@@\{user.getDeviceId().replaceAll(":", "_")}";
            mqttServerPublish.wakeup(topic);
            TimeUnit.SECONDS.sleep(2);
        }

        return Option.of(sessionManager.getSessionByDeviceId(user.getDeviceId()))
                .toEither(BizError.of(400, "设备未连接"))
                .filterOrElse(it -> !it.isPlaying(), _ -> BizError.of(400, "设备说话中，请稍后再试"))
                .map(newSession -> dialogueService.sendOneSentence(newSession, "You're so creative in thinking! But we're using English now. The English word for \"六\" is \"six\". It starts with \"s\" which doesn't match our game rule, this time. Now I said \"train\" before,so you need to find a word starting with \"n\". Can you think of one?", false))
                .filterOrElse(it -> it != null, _ -> BizError.of(400, "设备说话中，请稍后再试"));
    }

    @PassAuth
    @PostMapping("/p4t")
    @Operation(summary = "主动推送场景")
    public Either<BizError, ?> push4test(@RequestBody PushTestParams params) {
        var topic = STR."devices/p2p/GID@@@\{params.deviceId.replaceAll(":", "_")}";
        return Option.of(params.deviceId)
                .toEither(BizError.UserNotExists)
                .map(_ -> {
                    if (params.action.equals("open")) {
                        mqttServerPublish.open(topic, "player", params.category);
                    } else {
                        mqttServerPublish.close(topic, "player");
                    }
                    return true;
                });
    }

    public record PushTestParams(String deviceId, String action, String category) {}

    public record PushParams(String scene) {
    }
}
