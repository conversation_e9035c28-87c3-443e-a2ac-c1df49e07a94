package com.xiaozhi.schedule;

import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.communication.common.SessionManager;
import com.xiaozhi.communication.domain.TaskChain;
import com.xiaozhi.communication.server.mqtt.MqttServerPublish;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.dao.MediaMapper;
import com.xiaozhi.dao.TaskInstanceMapper;
import com.xiaozhi.dialogue.domain.StartParams;
import com.xiaozhi.dialogue.llm.ChatService;
import com.xiaozhi.dialogue.service.AudioPushService;
import com.xiaozhi.dialogue.service.DialogueService;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.enums.TaskContent;
import com.xiaozhi.enums.TaskStatus;
import com.xiaozhi.utils.CozeUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class ExecuteTaskInstanceJob {

    @Resource
    private ManagerMapper managerMapper;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Resource
    private ChatService chatService;

    @Resource
    private SessionManager sessionManager;

    @Resource
    private DialogueService dialogueService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MqttServerPublish mqttServerPublish;

    @Resource
    private MediaMapper mediaMapper;

    @Resource
    private AudioPushService audioPushService;

    private final String TaskDelayQueue = "xiaozhi:task:queue";

    // Lua 脚本：原子地取出并删除 0-now 之间的数据
    private final String ZRangeScript = """
            local msgs = redis.call('ZRANGEBYSCORE', KEYS[1], 0, ARGV[1], 'limit', 0, 100)
            if (#msgs > 0) then
            redis.call('ZREM', KEYS[1], unpack(msgs))
            return msgs
            else return {} end
            """;

    @Scheduled(cron = "0 */1 * * * ?")
    public void run() {
        var endTime = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        var startTime = endTime - 60 * 1000;
        var taskJsonArray = Optional.ofNullable(stringRedisTemplate.opsForZSet().rangeByScore(TaskDelayQueue, startTime, endTime))
                .orElseGet(Set::of);

        log.info("From {} to {} with zone {} get tasks {}", startTime, endTime, ZoneId.systemDefault(), taskJsonArray);

        var tasks = taskJsonArray.stream()
                .map(Integer::valueOf)
                .toList();

        for (var taskId : tasks) {
            var instance = taskInstanceMapper.selectById(taskId);
            if (instance == null) continue;
            if (instance.getStatus() != TaskStatus.Waiting) continue;

            var now = Instant.now();

            // create task chain
            var durations = new ArrayList<Integer>();
            for (int i = 0; i < instance.getDuration().length(); i += 2) {
                durations.add(Integer.parseInt(instance.getDuration().substring(i, i + 2)));
            }

            var idx = new AtomicInteger(0);
            var dummy = new TaskChain();
            Arrays.stream(TaskContent.values())
                    .filter(it -> (it.getValue() & instance.getContent()) != 0)
                    .map(it -> new TaskChain().setInstanceId(instance.getId()).setContent(it).setStartTime(now).setDuration(durations.get(idx.getAndIncrement())))
                    .reduce(dummy, (z, x) -> {
                        z.setNext(x);
                        return x;
                    }, (_, it) -> it);

            var taskChain = dummy.getNext();
            var topic = STR."devices/p2p/GID@@@\{instance.getDeviceId().replaceAll(":", "_")}";

            var session = sessionManager.getSessionByDeviceId(instance.getDeviceId());
            if (session != null && session.isPlaying()) {
                session.setIsCurrTaskDone(false);
                continue;
            }

            switch (instance.getType()) {
                // case Listening, Information -> {
                //     mqttServerPublish.open(topic, "player", "news");
                //     sessionManager.putListeningStopTime(instance.getDeviceId(), Instant.now().plusSeconds(taskChain.getDuration() * 60));
                //     instance.setStatus(TaskStatus.Running);
                //     taskInstanceMapper.updateById(instance);
                //     // remove when task started
                //     stringRedisTemplate.opsForZSet().remove(TaskDelayQueue, taskId.toString());
                // }

                case Listening -> {
                    var media = mediaMapper.findLatest("story");
                    if (media == null) return;
                    mqttServerPublish.send(topic, "{\"type\":\"player\"}");

                    Thread.startVirtualThread(() -> {
                        try {
                            TimeUnit.SECONDS.sleep(2);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }

                        var newSession = sessionManager.getSessionByDeviceId(instance.getDeviceId());
                        if (newSession == null) return;

                        instance.setStatus(TaskStatus.Running);
                        taskInstanceMapper.updateById(instance);
                        // remove when task started
                        stringRedisTemplate.opsForZSet().remove(TaskDelayQueue, taskId.toString());

                        audioPushService.sendAudioMessage(newSession, media.getAssetUrl(), media.getTitle(), true, true)
                                .thenRun(newSession::sendGoodbye);
                    });
                }

                case Information -> {
                    var media = mediaMapper.findLatest("news");
                    if (media == null) return;
                    mqttServerPublish.send(topic, "{\"type\":\"player\"}");

                    Thread.startVirtualThread(() -> {
                        try {
                            TimeUnit.SECONDS.sleep(2);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }

                        var newSession = sessionManager.getSessionByDeviceId(instance.getDeviceId());
                        if (newSession == null) return;

                        instance.setStatus(TaskStatus.Running);
                        taskInstanceMapper.updateById(instance);
                        // remove when task started
                        stringRedisTemplate.opsForZSet().remove(TaskDelayQueue, taskId.toString());

                        audioPushService.sendAudioMessage(newSession, media.getAssetUrl(), media.getTitle(), true, true)
                                .thenRun(newSession::sendGoodbye);
                    });

                }
                case Bedtime, Conversation -> {
                    // wakeup
                    // if (session == null || !session.isOpen()) {
                    // }
                    mqttServerPublish.wakeup(topic);
                    Thread.startVirtualThread(() -> {
                        try {
                            TimeUnit.SECONDS.sleep(2);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                        var newSession = sessionManager.getSessionByDeviceId(instance.getDeviceId());
                        if (newSession == null) return;
                        newSession.setTaskChain(taskChain);

                        if (newSession.isPlaying()) {
                            // 设备正在播放，将任务置为取消
                            instance.setStatus(TaskStatus.Canceled);
                            taskInstanceMapper.updateById(instance);
                            return;
                        }

                        var managerQuery = new OhMyLambdaQueryWrapper<Manager>()
                                .eq(Manager::getId, instance.getManagerId())
                                .select(Manager::getId, Manager::getName, Manager::getCefr, Manager::getStarterConvId);
                        var manager = managerMapper.selectOne(managerQuery);

                        var startParams = new StartParams()
                                .setScene("【】")
                                .setName(manager.getName())
                                .setCefr(manager.getCefr())
                                .setUserId(manager.getId().toString())
                                .setConversationId(manager.getStarterConvId());
                        var hello = CozeUtil.genStartSentence(startParams);
                        dialogueService.sendOneSentence(newSession, hello, true)
                                .thenRun(() -> {
                                    instance.setStatus(TaskStatus.Running);
                                    taskInstanceMapper.updateById(instance);
                                    // remove when task started
                                    stringRedisTemplate.opsForZSet().remove(TaskDelayQueue, taskId.toString());
                                });
                    });

                }
            }

        }
    }
}
