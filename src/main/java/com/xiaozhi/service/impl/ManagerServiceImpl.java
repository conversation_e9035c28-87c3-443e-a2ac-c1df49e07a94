package com.xiaozhi.service.impl;

import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.service.ManagerService;
import com.xiaozhi.vo.ManagerUpdateParams;
import io.vavr.control.Either;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class ManagerServiceImpl implements ManagerService {

    @Resource
    private ManagerMapper managerMapper;

    @Override
    public Either<BizError, Manager> detail(Integer id) {
        var query = new OhMyLambdaQueryWrapper<Manager>()
                .eq(Manager::getId, id)
                .select(Manager::getId, Manager::getName, Manager::getBirth, Manager::getGender, Manager::getCefr, Manager::getStarterConvId);

        return Option.of(managerMapper.selectOne(query))
                .toEither(BizError.UserNotExists);
    }

    @Override
    public Either<BizError, ?> update(Integer id, ManagerUpdateParams form) {
        var query = new OhMyLambdaQueryWrapper<Manager>()
                .eq(Manager::getId, id)
                .select(Manager::getId);

        return Option.of(managerMapper.selectOne(query))
                .toEither(BizError.UserNotExists)
                .map(it -> {
                    it.setName(form.getName());
                    it.setCefr(form.getCefr());
                    it.setBirth(form.getBirth());
                    it.setGender(form.getGender());
                    managerMapper.updateById(it);
                    return true;
                });
    }

    @Override
    public Manager findBy(String deviceId) {
        var query = new OhMyLambdaQueryWrapper<Manager>()
                .eq(Manager::getDeviceId, deviceId)
                .select(Manager::getId, Manager::getName, Manager::getBirth, Manager::getCefr);

        return managerMapper.selectOne(query);
    }
}
