:header = <<
Content-Type: application/json
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36
Authorization: eyJhbGciOiJIUzI1NiJ9.eyJpZCI6MSwidXQiOjMsImV4cCI6MTc1ODU5Nzc2MX0.UaJDkBnZ8ur216ska1VDLRFNEGsenVVgTbkg5ZZO2f8

# Authorization: eyJhbGciOiJIUzI1NiJ9.eyJpZCI6MSwidXQiOjEsImV4cCI6MTc1ODQzODczMX0.TLEiSNj0-jP2EbaYRpQViqC0FC-uulHHd8waguTb3fM


#
# :host = https://rdmytikasapitest.lezhilong.cn/xiaozhi-esp32-server/api/v1
:host = http://localhost:8091/api/v1

#

GET https://rdmytikasapitest.lezhilong.cn/xiaozhi-esp32-server/api/v1/d/medias?category=news|story
Authorization: 90:e5:b1:a9:8c:7c

#
GET :host/m/daily-stats
:header

#
POST :host/m/sms-code
:header

{
  "mobile": "16710245700"
}

#
POST :host/m/login
:header

{
  "mobile": "16710245700",
  "code": "245700"
}

#
POST :host/m/p4t
:header

{
  "device_id": "",
  "action": "open",
  "category": "news"
}

#
POST :host/m/pa
:header

{
  "sence": "早餐"
}


#
GET :host/devices/wakeup?device_id=90:e5:b1:a9:8c:7c
:header

#
PUT :host/ota/version
:header

{
  "version": "2.0.1",
  "url": "https://mytikas-testing.oss-cn-beijing.aliyuncs.com/xiaozhi/xiaozhi.bin"
}

#
POST :host/m/p4t
:header

{
  "device_id": "90:e5:b1:a9:8c:7c",
  "action": "open",
  "category": "news"
}

#
POST :host/medias/gen
:header

{
  "title": "为什么我们会对某些事情“起鸡皮疙瘩”？",
  "category": "story"
}

#
POST :host/medias/gen
:header

{
  "title": "生成今日资讯",
  "category": "news"
}

#
PUT :host/m/tasks/12
:header

{
  "time":"15:13",
  "week_mask":127
}